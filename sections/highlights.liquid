{% assign sectionId = section.id %}
{{ 'section-main-product.css' | asset_url | stylesheet_tag }}

<section class="highlights-section" aria-label="Highlights">
  <style>
    .highlights-section {
      background: #fff;
      border-radius: 12px;
      padding: 2.5rem 1.5rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      margin: 0 auto;
      max-width: var(--page-width, 1200px);
    }
    .why-heading {
      font-size: clamp(1.5rem, 2.8vw, 2rem);
      font-weight: 800;
      color: #111827;
      text-align: center;
      margin: 0 0 2rem 0;
      letter-spacing: -0.01em;
    }

    .highlights-grid {
      display: grid;
      grid-template-columns: repeat(4, minmax(0, 1fr));
      gap: 2rem 1.5rem;
      align-items: start;
    }
    /* Force a single row on desktop: one column per block */
    @media (min-width: 769px) {
      #highlights-grid-{{ sectionId }} { grid-template-columns: repeat({{ section.blocks.size }}, minmax(0, 1fr)); }
    }
    @media (max-width: 1024px) {
      .highlights-grid { grid-template-columns: repeat(3, 1fr); }
    }
    @media (max-width: 768px) {
      /* Mobile slider */
      .highlights-grid {
        display: flex;
        overflow-x: auto;
        gap: 1rem;
        padding: 0 0.5rem;
        scroll-snap-type: x mandatory;
        -webkit-overflow-scrolling: touch;
      }
      .highlights-grid::-webkit-scrollbar { display: none; }
      .highlight-item { flex: 0 0 80%; scroll-snap-align: start; }
    }

    .highlight-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: 0.5rem;
      background: transparent;
      border: 0;
      box-shadow: none;
      padding: 0.5rem;
      min-height: unset;
    }

    .highlight-icon-container {
      --bg: var(--icon-bg, #F3F4F6);
      width: 64px;
      height: 64px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: 9999px;
      background: var(--bg);
      box-shadow: 0 4px 14px rgba(0,0,0,0.06);
    }

    .highlight-icon-img {
      width: 28px;
      height: 28px;
      object-fit: contain;
      border-radius: 6px;
    }

    .highlight-title {
      font-weight: 700;
      font-size: 1.125rem;
      color: #111827;
      margin-top: 0.75rem;
    }

    .highlight-desc {
      font-size: 0.95rem;
      line-height: 1.6;
      color: #4B5563;
      max-width: 28ch;
      margin: 0.25rem auto 0;
    }

    /* Pagination dots (shown on mobile) */
    .highlights-pagination { display: none; justify-content: center; gap: 8px; margin-top: 14px; }
    .highlights-pagination .hl-dot { width: 8px; height: 8px; border-radius: 9999px; border: none; background: #e5e7eb; padding: 0; cursor: pointer; }
    .highlights-pagination .hl-dot.active { background: #111827; transform: scale(1.1); }
    @media (max-width: 768px) { .highlights-pagination { display: flex; } }
  </style>

  {% if section.settings.title != blank %}
    <h2 class="why-heading">{{ section.settings.title }}</h2>
  {% endif %}

  <div id="highlights-grid-{{ sectionId }}" class="highlights-grid">
    {% for block in section.blocks %}
      {% case block.type %}
        {% when 'item' %}
          <div class="highlight-item" {{ block.shopify_attributes }} style="--icon-bg: {{ block.settings.icon_bg }};">
            <div class="highlight-icon-container">
              {% if block.settings.icon != blank %}
                {{ block.settings.icon | image_url: width: 80 | image_tag: loading: 'lazy', class: 'highlight-icon-img', alt: block.settings.title | escape }}
              {% elsif block.settings.icon_asset != 'none' %}
                <img src="{{ block.settings.icon_asset | asset_url }}" alt="{{ block.settings.title | escape }}" class="highlight-icon-img" loading="lazy">
              {% else %}
                <span aria-hidden="true" class="highlight-icon-img" style="width:24px;height:24px;">★</span>
              {% endif %}
            </div>
            {% if block.settings.title != blank %}
              <div class="highlight-title">{{ block.settings.title }}</div>
            {% endif %}
            {% if block.settings.description != blank %}
              <div class="highlight-desc">{{ block.settings.description }}</div>
            {% endif %}
          </div>
      {% endcase %}
    {% endfor %}
  </div>
  <div id="highlights-pagination-{{ sectionId }}" class="highlights-pagination" aria-hidden="true"></div>

  <script>
    (function(){
      const grid = document.getElementById('highlights-grid-{{ sectionId }}');
      const dotsWrap = document.getElementById('highlights-pagination-{{ sectionId }}');
      if (!grid || !dotsWrap) return;

      function buildDots(){
        dotsWrap.innerHTML = '';
        const slides = Array.from(grid.children);
        slides.forEach((_, idx) => {
          const b = document.createElement('button');
          b.className = 'hl-dot';
          b.setAttribute('aria-label', 'Go to slide ' + (idx + 1));
          b.addEventListener('click', () => {
            const target = slides[idx];
            grid.scrollTo({ left: target.offsetLeft - grid.offsetLeft, behavior: 'smooth' });
          });
          dotsWrap.appendChild(b);
        });
        setActive(0);
      }

      function setActive(activeIdx){
        const dots = dotsWrap.querySelectorAll('.hl-dot');
        dots.forEach((d, i) => d.classList.toggle('active', i === activeIdx));
      }

      function onScroll(){
        if (window.innerWidth > 768) return; // only when mobile layout is active
        const slides = Array.from(grid.children);
        let nearest = 0;
        let best = Infinity;
        const left = grid.scrollLeft;
        slides.forEach((el, i) => {
          const dist = Math.abs(el.offsetLeft - left);
          if (dist < best) { best = dist; nearest = i; }
        });
        setActive(nearest);
      }

      buildDots();
      grid.addEventListener('scroll', onScroll, { passive: true });
      window.addEventListener('resize', onScroll);
    })();
  </script>
</section>

{% schema %}
{
  "name": "Highlights",
  "tag": "section",
  "class": "section",
  "settings": [
    { "type": "text", "id": "title", "label": "Heading", "default": "Why book with us?" }
  ],
  "blocks": [
    {
      "type": "item",
      "name": "Highlight",
      "settings": [
        { "type": "text", "id": "title", "label": "Title", "default": "24/7 customer support" },
        { "type": "textarea", "id": "description", "label": "Description", "default": "No matter the time zone, we’re here to help." },
        { "type": "color", "id": "icon_bg", "label": "Icon background", "default": "#FFF2F1" },
        { "type": "select", "id": "icon_asset", "label": "Icon (built-in)", "options": [
          { "value": "icon-phone.svg", "label": "Phone" },
          { "value": "icon-star.svg", "label": "Star" },
          { "value": "icon-heart.svg", "label": "Heart" },
          { "value": "icon-map-pin.svg", "label": "Map pin" },
          { "value": "icon-calendar.svg", "label": "Calendar" },
          { "value": "icon-checkmark.svg", "label": "Check" },
          { "value": "icon-leaf.svg", "label": "Leaf" },
          { "value": "icon-box.svg", "label": "Box" },
          { "value": "icon-plane.svg", "label": "Plane" }
        ], "default": "icon-star.svg" },
        { "type": "image_picker", "id": "icon", "label": "Custom icon image (optional)" }
      ]
    }
  ],
  "presets": [
    {
      "name": "Highlights",
      "blocks": [
        { "type": "item" },
        { "type": "item" },
        { "type": "item" },
        { "type": "item" }
      ]
    }
  ]
}
{% endschema %} 