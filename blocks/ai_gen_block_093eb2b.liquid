{% doc %}
  @prompt
    Create three visible before/after sliders in a row. Each slider should have: a container div with fixed height 300px, two img elements positioned absolutely, the top image with clip-path or width that changes with a range input below each image. Use inline styles for visibility, set background colors for containers, add borders to make elements visible. Include schema settings for 6 images total. Focus on making it visible first, then functional. Use simple CSS and minimal JavaScript.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-before-after-container-{{ ai_gen_id }} {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: center;
    padding: 20px;
    /* RTL-safe container */
    direction: ltr !important;
  }

  .ai-slider-wrapper-{{ ai_gen_id }} {
    flex: 1;
    min-width: 250px;
    max-width: 400px;
    /* RTL-safe wrapper */
    direction: ltr !important;
  }

  .ai-image-container-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: 300px;
    border: 3px solid #333;
    background-color: #f0f0f0;
    overflow: hidden;
    border-radius: 8px;
    /* RTL-safe image container */
    direction: ltr !important;
  }

  .ai-before-image-{{ ai_gen_id }}, .ai-after-image-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    /* RTL-safe images */
    direction: ltr !important;
  }

  .ai-after-image-{{ ai_gen_id }} {
    clip-path: inset(0 50% 0 0);
    /* RTL-safe clipping */
    direction: ltr !important;
  }

  .ai-image-placeholder-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-size: 14px;
  }

  .ai-image-placeholder-{{ ai_gen_id }} svg {
    width: 80px;
    height: 80px;
    opacity: 0.5;
  }

  .ai-slider-control-{{ ai_gen_id }} {
    margin-top: 15px;
    /* RTL-safe slider control */
    direction: ltr !important;
  }

  .ai-slider-input-{{ ai_gen_id }} {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
    /* RTL-safe slider input */
    direction: ltr !important;
  }

  .ai-slider-input-{{ ai_gen_id }}::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: {{ block.settings.slider_color }};
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }

  .ai-slider-input-{{ ai_gen_id }}::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: {{ block.settings.slider_color }};
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }

  .ai-slider-labels-{{ ai_gen_id }} {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: #666;
    /* RTL-safe labels */
    direction: ltr !important;
  }

  .ai-divider-line-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: {{ block.settings.divider_color }};
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
    /* RTL-safe divider */
    direction: ltr !important;
  }

  /* RTL-safe positioning for all elements */
  [dir="rtl"] .ai-before-after-container-{{ ai_gen_id }},
  [dir="rtl"] .ai-slider-wrapper-{{ ai_gen_id }},
  [dir="rtl"] .ai-image-container-{{ ai_gen_id }},
  [dir="rtl"] .ai-slider-control-{{ ai_gen_id }},
  [dir="rtl"] .ai-slider-labels-{{ ai_gen_id }} {
    direction: ltr !important;
  }

  /* Ensure labels stay in correct order */
  .ai-slider-labels-{{ ai_gen_id }} span:first-child {
    order: 1;
  }

  .ai-slider-labels-{{ ai_gen_id }} span:last-child {
    order: 2;
  }

  @media screen and (max-width: 768px) {
    .ai-before-after-container-{{ ai_gen_id }} {
      flex-direction: column;
      gap: 30px;
      direction: ltr !important;
    }

    .ai-slider-wrapper-{{ ai_gen_id }} {
      max-width: 100%;
      direction: ltr !important;
    }
  }
{% endstyle %}

<before-after-sliders-{{ ai_gen_id }}
  class="ai-before-after-container-{{ ai_gen_id }}"
  {{ block.shopify_attributes }}
>
  {% for i in (1..3) %}
    {% liquid
      assign before_image_key = 'before_image_' | append: i
      assign after_image_key = 'after_image_' | append: i
      assign before_image = block.settings[before_image_key]
      assign after_image = block.settings[after_image_key]
    %}
    
    <div class="ai-slider-wrapper-{{ ai_gen_id }}">
      <div class="ai-image-container-{{ ai_gen_id }}" data-slider="{{ i }}">
        {% if before_image %}
          <img
            src="{{ before_image | image_url: width: 800 }}"
            alt="{{ before_image.alt | escape }}"
            class="ai-before-image-{{ ai_gen_id }}"loading="lazy"
          >
        {% else %}
          <div class="ai-image-placeholder-{{ ai_gen_id }}">
            {{ 'image' | placeholder_svg_tag }}
          </div>
        {% endif %}

        {% if after_image %}
          <img
            src="{{ after_image | image_url: width: 800 }}"
            alt="{{ after_image.alt | escape }}"
            class="ai-after-image-{{ ai_gen_id }}"
            loading="lazy"
          >
        {% else %}
          <div class="ai-image-placeholder-{{ ai_gen_id }}" style="background-color: #d0d0d0;">
            {{ 'image' | placeholder_svg_tag }}
          </div>
        {% endif %}

        {% if block.settings.show_divider %}
          <div class="ai-divider-line-{{ ai_gen_id }}"></div>
        {% endif %}
      </div>

      <div class="ai-slider-control-{{ ai_gen_id }}">
        <input
          type="range"
          min="0"
          max="100"
          value="50"
          class="ai-slider-input-{{ ai_gen_id }}"
          data-slider-input="{{ i }}"
        >
        {% if block.settings.show_labels %}
          <div class="ai-slider-labels-{{ ai_gen_id }}">
            <span>Before</span>
            <span>After</span>
          </div>
        {% endif %}
      </div>
    </div>
  {% endfor %}
</before-after-sliders-{{ ai_gen_id }}>

<script>
  (function() {
    class BeforeAfterSliders{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
      }

      connectedCallback() {
        this.sliders = this.querySelectorAll('.ai-slider-input-{{ ai_gen_id }}');
        this.setupSliders();
      }

      setupSliders() {
        this.sliders.forEach((slider) => {
          const sliderId = slider.getAttribute('data-slider-input');
          const container = this.querySelector(`[data-slider="${sliderId}"]`);
          const afterImage = container.querySelector('.ai-after-image-{{ ai_gen_id }}');
          const dividerLine = container.querySelector('.ai-divider-line-{{ ai_gen_id }}');

          slider.addEventListener('input', (e) => {
            const value = e.target.value;

            // Check if page is in RTL mode
            const isRTL = document.documentElement.dir === 'rtl' ||
                         document.body.dir === 'rtl' ||
                         getComputedStyle(document.documentElement).direction === 'rtl';

            let clipPercentage, dividerPosition;

            if (isRTL) {
              // In RTL: invert the logic so circle position matches image reveal
              clipPercentage = value; // Use value directly
              dividerPosition = value;
            } else {
              // In LTR: use original logic
              clipPercentage = 100 - value;
              dividerPosition = value;
            }

            if (afterImage) {
              afterImage.style.clipPath = `inset(0 ${clipPercentage}% 0 0)`;
              afterImage.style.direction = 'ltr';
            }

            if (dividerLine) {
              dividerLine.style.left = `${dividerPosition}%`;
              dividerLine.style.transform = 'translateX(-50%)';
              dividerLine.style.direction = 'ltr';
            }
          });

          slider.dispatchEvent(new Event('input'));
        });
      }
    }

    customElements.define('before-after-sliders-{{ ai_gen_id }}', BeforeAfterSliders{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "Before After Sliders",
  "settings": [
    {
      "type": "header",
      "content": "Slider 1"
    },
    {
      "type": "image_picker",
      "id": "before_image_1",
      "label": "Before image 1"
    },
    {
      "type": "image_picker",
      "id": "after_image_1",
      "label": "After image 1"
    },
    {
      "type": "header",
      "content": "Slider 2"
    },
    {
      "type": "image_picker",
      "id": "before_image_2",
      "label": "Before image 2"
    },
    {
      "type": "image_picker",
      "id": "after_image_2",
      "label": "After image 2"
    },
    {
      "type": "header",
      "content": "Slider 3"
    },
    {
      "type": "image_picker",
      "id": "before_image_3",
      "label": "Before image 3"
    },
    {
      "type": "image_picker",
      "id": "after_image_3",
      "label": "After image 3"
    },
    {
      "type": "header",
      "content": "Style Settings"
    },
    {
      "type": "color",
      "id": "slider_color",
      "label": "Slider handle color",
      "default": "#007cba"
    },
    {
      "type": "color",
      "id": "divider_color",
      "label": "Divider line color",
      "default": "#ffffff"
    },
    {
      "type": "checkbox",
      "id": "show_divider",
      "label": "Show divider line",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_labels",
      "label": "Show before/after labels",
      "default": true
    }
  ],
  "presets": [
    {
      "name": "Before After Sliders"
    }
  ]
}
{% endschema %}