/* Google Translate Widget Styles */
.header-google-translate {
  display: inline-flex;
  align-items: center;
  margin-right: 1rem;
  position: relative;
}

.gt_switcher_wrapper {
  position: relative;
  display: inline-block;
}

.gt_switcher {
  position: relative;
  display: inline-block;
  cursor: pointer;
  font-family: var(--font-body-family);
}

.gt_selected {
  display: flex;
  align-items: center;
  padding: 0.6rem 1rem;
  border: 1px solid rgba(var(--color-foreground), 0.2);
  border-radius: 4px;
  background-color: rgb(var(--color-background));
  min-width: 120px;
  transition: all 0.2s ease;
  position: relative;
}

.gt_selected:hover {
  border-color: rgba(var(--color-foreground), 0.4);
  box-shadow: 0 2px 8px rgba(var(--color-foreground), 0.1);
}

.gt_selected a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: rgb(var(--color-foreground));
  font-size: 1.4rem;
  font-weight: 400;
  width: 100%;
}

.gt_selected img {
  margin-right: 8px;
  border-radius: 2px;
}

.gt_selected:after {
  content: '';
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgb(var(--color-foreground));
  transition: transform 0.2s ease;
}

.gt_switcher:hover .gt_selected:after {
  transform: translateY(-50%) rotate(180deg);
}

.gt_option {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  background-color: rgb(var(--color-background));
  border: 1px solid rgba(var(--color-foreground), 0.2);
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(var(--color-foreground), 0.15);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  overflow: hidden;
}

.gt_switcher:hover .gt_option {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.gt_option a {
  display: flex;
  align-items: center;
  padding: 0.8rem 1rem;
  text-decoration: none;
  color: rgb(var(--color-foreground));
  font-size: 1.4rem;
  border-bottom: 1px solid rgba(var(--color-foreground), 0.1);
  transition: background-color 0.2s ease;
}

.gt_option a:last-child {
  border-bottom: none;
}

.gt_option a:hover {
  background-color: rgba(var(--color-foreground), 0.05);
}

.gt_option img {
  margin-right: 8px;
  border-radius: 2px;
}

/* Hide the default Google Translate elements */
#google_translate_element,
.goog-te-banner-frame,
.goog-te-menu-frame {
  display: none !important;
}

.goog-te-gadget {
  display: none !important;
}

/* Hide Google Translate top bar */
body {
  top: 0 !important;
}

.goog-te-banner-frame.skiptranslate {
  display: none !important;
}

/* Mobile responsive */
@media screen and (max-width: 749px) {
  .header-google-translate {
    margin-right: 0.5rem;
  }
  
  .gt_selected {
    min-width: 100px;
    padding: 0.5rem 0.8rem;
  }
  
  .gt_selected a,
  .gt_option a {
    font-size: 1.2rem;
  }
  
  .gt_option {
    left: -20px;
    right: -20px;
  }
}

@media screen and (max-width: 550px) {
  .gt_selected {
    min-width: 80px;
    padding: 0.4rem 0.6rem;
  }
  
  .gt_selected span {
    display: none;
  }
  
  .gt_option a span {
    display: inline;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .gt_selected {
    background-color: rgb(var(--color-background));
    border-color: rgba(var(--color-foreground), 0.3);
  }
  
  .gt_option {
    background-color: rgb(var(--color-background));
    border-color: rgba(var(--color-foreground), 0.3);
  }
  
  .gt_option a:hover {
    background-color: rgba(var(--color-foreground), 0.1);
  }
}

/* Integration with header styles */
.header__icons .header-google-translate {
  margin-right: 1.5rem;
}

.header__icons .header-google-translate:last-child {
  margin-right: 0;
}

/* Ensure proper z-index in header context */
.header .gt_option {
  z-index: 101;
}

/* Animation for smooth transitions */
.gt_switcher * {
  transition: all 0.2s ease;
}

/* Focus states for accessibility */
.gt_selected:focus-within,
.gt_option a:focus {
  outline: 2px solid rgb(var(--color-foreground));
  outline-offset: 2px;
}

/* Loading state */
.gt_switcher.loading .gt_selected:after {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: translateY(-50%) rotate(0deg); }
  to { transform: translateY(-50%) rotate(360deg); }
}
