{% comment %}
  Google Translate Widget for Header
  Renders a Google Translate dropdown in the header
{% endcomment %}

<div class="header-google-translate">
  <div id="google_translate_element"></div>
  
  <div class="gt_switcher_wrapper">
    <div class="gt_switcher notranslate">
      <div class="gt_selected">
        <a href="#" class="gt_current_lang">
          <img src="https://cdn.gtranslate.net/flags/16/en.png" height="16" width="16" alt="en"> 
          <span>English</span>
        </a>
      </div>
      <div class="gt_option">
        <a href="#" title="English" class="nturl gt_current" data-gt-lang="en">
          <img width="16" height="16" alt="en" src="https://cdn.gtranslate.net/flags/16/en.png"> English
        </a>
        <a href="#" title="עִבְרִית" class="nturl" data-gt-lang="iw">
          <img width="16" height="16" alt="iw" src="https://cdn.gtranslate.net/flags/16/iw.png"> עִבְרִית
        </a>
        <a href="#" title="Русский" class="nturl" data-gt-lang="ru">
          <img width="16" height="16" alt="ru" src="https://cdn.gtranslate.net/flags/16/ru.png"> Русский
        </a>
        <a href="#" title="Español" class="nturl" data-gt-lang="es">
          <img width="16" height="16" alt="es" src="https://cdn.gtranslate.net/flags/16/es.png"> Español
        </a>
      </div>
    </div>
  </div>
</div>

<style>
.header-google-translate {
  display: inline-block;
  margin-right: 1rem;
}

.gt_switcher_wrapper {
  position: relative;
  display: inline-block;
}

.gt_switcher {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.gt_selected {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border: 1px solid #cccccc;
  border-radius: 4px;
  background-color: #ffffff;
  min-width: 120px;
}

.gt_selected a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #666666;
  font-size: 14px;
}

.gt_selected img {
  margin-right: 8px;
}

.gt_selected:after {
  content: '▼';
  margin-left: auto;
  font-size: 10px;
  color: #666666;
}

.gt_option {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #eeeeee;
  border: 1px solid #cccccc;
  border-top: none;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  z-index: 1000;
  display: none;
}

.gt_switcher:hover .gt_option {
  display: block;
}

.gt_option a {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  text-decoration: none;
  color: #000000;
  font-size: 14px;
  border-bottom: 1px solid #ddd;
}

.gt_option a:last-child {
  border-bottom: none;
}

.gt_option a:hover {
  background-color: #ffffff;
}

.gt_option img {
  margin-right: 8px;
}

/* Hide the default Google Translate element */
#google_translate_element {
  display: none;
}

/* Mobile responsive */
@media screen and (max-width: 749px) {
  .header-google-translate {
    margin-right: 0.5rem;
  }
  
  .gt_selected {
    min-width: 100px;
    padding: 0.4rem;
  }
  
  .gt_selected a,
  .gt_option a {
    font-size: 12px;
  }
}
</style>

<script>
window.gtranslateSettings = {
  "default_language": "en",
  "languages": ["en", "iw", "ru", "es"],
  "url_structure": "none",
  "native_language_names": true,
  "detect_browser_language": false,
  "flag_style": "3d",
  "flag_size": 16,
  "wrapper_selector": "#google_translate_element",
  "alt_flags": {},
  "custom_domains": null
};

function googleTranslateElementInit() {
  new google.translate.TranslateElement({
    pageLanguage: 'en',
    includedLanguages: 'en,iw,ru,es',
    layout: google.translate.TranslateElement.InlineLayout.SIMPLE,
    autoDisplay: false
  }, 'google_translate_element');
}

// Load Google Translate script
if (!window.googleTranslateLoaded) {
  window.googleTranslateLoaded = true;
  var script = document.createElement('script');
  script.type = 'text/javascript';
  script.src = 'https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit';
  document.head.appendChild(script);
}

// Handle custom dropdown clicks
document.addEventListener('DOMContentLoaded', function() {
  const switcher = document.querySelector('.gt_switcher');
  const options = document.querySelectorAll('.gt_option a');
  const selected = document.querySelector('.gt_selected a');
  
  if (switcher && options && selected) {
    options.forEach(function(option) {
      option.addEventListener('click', function(e) {
        e.preventDefault();
        const lang = this.getAttribute('data-gt-lang');
        const flag = this.querySelector('img').src;
        const text = this.textContent.trim();
        
        // Update selected display
        selected.querySelector('img').src = flag;
        selected.querySelector('span').textContent = text;
        
        // Trigger Google Translate
        const selectElement = document.querySelector('.goog-te-combo');
        if (selectElement) {
          selectElement.value = lang;
          selectElement.dispatchEvent(new Event('change'));
        }
        
        // Hide dropdown
        document.querySelector('.gt_option').style.display = 'none';
      });
    });
  }
});
</script>
